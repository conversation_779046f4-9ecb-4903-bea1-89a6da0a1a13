/**
 * Content页面Favicon服务
 * 只在对应平台的content页面执行favicon获取，避免跨域问题
 */

import { MessagingService } from './messagingService'
import { MessageType } from '@/types'

export interface ContentFaviconResult {
  success: boolean
  blob?: Blob
  dataUrl?: string
  error?: string
  source?: string
}

export class ContentFaviconService {
  private static instance: ContentFaviconService

  private constructor() {}

  static getInstance(): ContentFaviconService {
    if (!ContentFaviconService.instance) {
      ContentFaviconService.instance = new ContentFaviconService()
    }
    return ContentFaviconService.instance
  }

  /**
   * 在当前页面获取favicon（只在content script中调用）
   */
  async getCurrentPageFavicon(): Promise<ContentFaviconResult> {
    try {
      console.log('【ContentFaviconService】开始获取当前页面favicon')
      
      // 获取当前页面的favicon
      const faviconUrl = await this.findCurrentPageFavicon()
      if (!faviconUrl) {
        return {
          success: false,
          error: 'No favicon found on current page'
        }
      }

      console.log('【ContentFaviconService】找到favicon URL:', faviconUrl)

      // 获取favicon数据
      const response = await fetch(faviconUrl)
      if (!response.ok) {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        }
      }

      const blob = await response.blob()
      
      // 验证是否为有效的图片
      if (!this.isValidImageBlob(blob)) {
        return {
          success: false,
          error: 'Invalid image format'
        }
      }

      // 转换为data URL
      const dataUrl = await this.blobToDataUrl(blob)

      console.log('【ContentFaviconService】成功获取favicon，大小:', blob.size, 'bytes')

      return {
        success: true,
        blob,
        dataUrl,
        source: faviconUrl
      }

    } catch (error) {
      console.error('【ContentFaviconService】获取favicon失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 查找当前页面的favicon
   */
  private async findCurrentPageFavicon(): Promise<string | null> {
    // 1. 查找HTML中的favicon链接
    const faviconLinks = document.querySelectorAll('link[rel*="icon"]')
    
    for (const link of faviconLinks) {
      const href = (link as HTMLLinkElement).href
      if (href && this.isValidFaviconUrl(href)) {
        return href
      }
    }

    // 2. 尝试常见的favicon路径
    const commonPaths = [
      '/favicon.svg',
      '/favicon.png',
      '/favicon.ico',
      '/apple-touch-icon.png',
      '/favicon-32x32.png',
      '/favicon-16x16.png'
    ]

    const baseUrl = `${window.location.protocol}//${window.location.host}`
    
    for (const path of commonPaths) {
      const faviconUrl = baseUrl + path
      try {
        const response = await fetch(faviconUrl, { method: 'HEAD' })
        if (response.ok) {
          return faviconUrl
        }
      } catch (error) {
        // 继续尝试下一个路径
        continue
      }
    }

    return null
  }

  /**
   * 验证favicon URL是否有效
   */
  private isValidFaviconUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname.toLowerCase()
      
      // 检查是否为图片格式
      const validExtensions = ['.ico', '.png', '.svg', '.jpg', '.jpeg', '.gif', '.webp']
      return validExtensions.some(ext => pathname.endsWith(ext)) || 
             pathname.includes('favicon') || 
             pathname.includes('icon')
    } catch {
      return false
    }
  }

  /**
   * 验证blob是否为有效图片
   */
  private isValidImageBlob(blob: Blob): boolean {
    const validTypes = [
      'image/x-icon',
      'image/vnd.microsoft.icon',
      'image/png',
      'image/svg+xml',
      'image/jpeg',
      'image/gif',
      'image/webp'
    ]
    
    return validTypes.includes(blob.type) && blob.size > 0 && blob.size < 1024 * 1024 // 1MB限制
  }

  /**
   * 将Blob转换为Data URL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 通过消息传递更新平台favicon（从content script调用）
   */
  async updateCurrentPlatformFavicon(platformId: number): Promise<boolean> {
    try {
      console.log('【ContentFaviconService】开始更新平台favicon:', platformId)
      
      // 获取当前页面的favicon
      const faviconResult = await this.getCurrentPageFavicon()
      if (!faviconResult.success || !faviconResult.blob) {
        console.error('【ContentFaviconService】获取favicon失败:', faviconResult.error)
        return false
      }

      // 通过消息传递发送到background script
      const response = await MessagingService.sendToBackground(MessageType.UPDATE_PLATFORM_FAVICON, {
        platformId,
        faviconBlob: faviconResult.blob,
        faviconUrl: faviconResult.source
      })

      if (response.success) {
        console.log('【ContentFaviconService】成功更新平台favicon')
        return true
      } else {
        console.error('【ContentFaviconService】更新平台favicon失败:', response.error)
        return false
      }

    } catch (error) {
      console.error('【ContentFaviconService】更新平台favicon异常:', error)
      return false
    }
  }

  /**
   * 检查当前页面是否需要更新favicon
   */
  async checkAndUpdateFaviconIfNeeded(platformId: number): Promise<boolean> {
    try {
      // 通过消息传递检查平台是否需要更新favicon
      const response = await MessagingService.sendToBackground(MessageType.CHECK_PLATFORM_FAVICON, {
        platformId
      })

      if (response.success && response.data?.needsUpdate) {
        console.log('【ContentFaviconService】检测到需要更新favicon，开始更新')
        return await this.updateCurrentPlatformFavicon(platformId)
      }

      return true // 不需要更新也算成功
      
    } catch (error) {
      console.error('【ContentFaviconService】检查favicon更新需求失败:', error)
      return false
    }
  }

  /**
   * 获取当前页面信息（用于调试）
   */
  getCurrentPageInfo(): {
    url: string
    hostname: string
    faviconLinks: string[]
  } {
    const faviconLinks = Array.from(document.querySelectorAll('link[rel*="icon"]'))
      .map(link => (link as HTMLLinkElement).href)
      .filter(href => href)

    return {
      url: window.location.href,
      hostname: window.location.hostname,
      faviconLinks
    }
  }
}

// 导出单例实例
export const contentFaviconService = ContentFaviconService.getInstance()
