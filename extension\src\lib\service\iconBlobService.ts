/**
 * 图标BLOB存储服务
 * 负责favicon的BLOB存储、读取和转换
 */

import { Platform } from '@/types/database'

export interface IconBlobResult {
  success: boolean
  dataUrl?: string
  blob?: Blob
  error?: string
  fromCache?: boolean
}

export class IconBlobService {
  private static instance: IconBlobService
  private blobCache: Map<number, string> = new Map() // platformId -> dataUrl
  private loadingSet: Set<number> = new Set()

  private constructor() {}

  static getInstance(): IconBlobService {
    if (!IconBlobService.instance) {
      IconBlobService.instance = new IconBlobService()
    }
    return IconBlobService.instance
  }

  /**
   * 获取平台图标（优先使用BLOB，fallback到URL）
   */
  async getPlatformIcon(platform: Platform): Promise<IconBlobResult> {
    try {
      const platformId = platform.id!
      
      // 检查内存缓存
      if (this.blobCache.has(platformId)) {
        return {
          success: true,
          dataUrl: this.blobCache.get(platformId)!,
          fromCache: true
        }
      }

      // 检查是否正在加载
      if (this.loadingSet.has(platformId)) {
        // 等待加载完成
        await this.waitForLoading(platformId)
        if (this.blobCache.has(platformId)) {
          return {
            success: true,
            dataUrl: this.blobCache.get(platformId)!,
            fromCache: true
          }
        }
      }

      this.loadingSet.add(platformId)

      try {
        // 1. 优先使用数据库中的BLOB数据
        if (platform.icon_blob) {
          console.log(`【IconBlobService】使用数据库BLOB数据: ${platform.name}`)
          const dataUrl = await this.blobToDataUrl(platform.icon_blob)
          this.blobCache.set(platformId, dataUrl)
          return {
            success: true,
            dataUrl,
            blob: platform.icon_blob
          }
        }

        // 2. 如果没有BLOB，返回失败（不再跨域获取）
        console.log(`【IconBlobService】平台${platform.name}没有BLOB数据，需要在对应页面更新`)
        return {
          success: false,
          error: 'No BLOB data available, needs to be updated from platform page'
        }

      } finally {
        this.loadingSet.delete(platformId)
      }

    } catch (error) {
      console.error('【IconBlobService】获取平台图标失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }







  /**
   * 将Blob转换为Data URL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 等待加载完成
   */
  private async waitForLoading(platformId: number, timeout = 10000): Promise<void> {
    const startTime = Date.now()
    while (this.loadingSet.has(platformId)) {
      if (Date.now() - startTime > timeout) {
        throw new Error('Loading timeout')
      }
      await this.delay(100)
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.blobCache.clear()
    this.loadingSet.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    cacheSize: number
    loadingCount: number
  } {
    return {
      cacheSize: this.blobCache.size,
      loadingCount: this.loadingSet.size
    }
  }
}

// 导出单例实例
export const iconBlobService = IconBlobService.getInstance()
