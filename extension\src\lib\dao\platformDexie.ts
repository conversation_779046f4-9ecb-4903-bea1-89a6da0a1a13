import { dexieDatabase } from '../database/dexie'
import {
  Platform,
  CreatePlatformInput,
  UpdatePlatformInput,
  DatabaseResult
} from '../../types/database'

export class PlatformService {
  private static instance: PlatformService

  public static getInstance(): PlatformService {
    if (!PlatformService.instance) {
      PlatformService.instance = new PlatformService()
    }
    return PlatformService.instance
  }

  /**
   * 创建平台
   */
  async create(input: CreatePlatformInput): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()

      const data: Omit<Platform, 'id'> = {
        name: input.name,
        url: input.url,
        icon: input.icon || null,
        icon_blob: input.icon_blob,
        is_delete: 0
      }

      const id = await dexieDatabase.platform.add(data as Platform)
      const record = await dexieDatabase.platform.get(id)

      return {
        success: true,
        data: record!
      }
    } catch (error) {
      console.error('Create platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID获取平台
   */
  async getById(id: number): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('id')
        .equals(id)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by id error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据名称获取平台
   */
  async getByName(name: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('name')
        .equals(name)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by name error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据URL获取平台
   */
  async getByUrl(url: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('url')
        .equals(url)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by url error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取所有平台
   */
  async getAll(): Promise<DatabaseResult<Platform[]>> {
    try {
      await dexieDatabase.initialize()
      
      const records = await dexieDatabase.platform
        .where('is_delete')
        .equals(0)
        .toArray()

      return {
        success: true,
        data: records
      }
    } catch (error) {
      console.error('Get all platforms error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新平台
   */
  async update(id: number, input: UpdatePlatformInput): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()

      await dexieDatabase.platform.update(id, input)
      const record = await dexieDatabase.platform.get(id)

      if (!record) {
        return {
          success: false,
          error: 'Platform not found after update'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Update platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 软删除平台
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.platform.update(id, { is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据域名匹配平台
   */
  async findByDomain(hostname: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const platforms = await dexieDatabase.platform
        .where('is_delete')
        .equals(0)
        .toArray()

      // 查找匹配的平台
      const matchedPlatform = platforms.find(platform => {
        try {
          const platformUrl = new URL(platform.url)
          return platformUrl.hostname === hostname || 
                 hostname.includes(platformUrl.hostname.replace('www.', ''))
        } catch {
          return false
        }
      })

      if (!matchedPlatform) {
        return {
          success: false,
          error: 'No matching platform found'
        }
      }

      return {
        success: true,
        data: matchedPlatform
      }
    } catch (error) {
      console.error('Find platform by domain error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 确保平台存在，不存在则创建
   */
  async ensurePlatform(name: string, url: string, icon?: string): Promise<DatabaseResult<Platform>> {
    try {
      // 先尝试根据名称获取
      const existingResult = await this.getByName(name)
      if (existingResult.success) {
        return existingResult
      }

      // 不存在则创建
      return await this.create({
        name,
        url,
        icon
      })
    } catch (error) {
      console.error('Ensure platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }


}

// 导出单例实例
export const platformService = PlatformService.getInstance()
